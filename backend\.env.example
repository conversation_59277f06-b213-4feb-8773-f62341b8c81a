# Gemini API Configuration
# GEMINI_API_KEY=your_gemini_api_key_here

# OpenAI API Configuration
OPENAI_API_KEY=sk-3OyW8vHYCaxON5Yq4Fd5D2AW6bpIyAKRqD8YAE0LdWGIzt4Q
OPENAI_BASE_URL=https://airouter.mxyhi.com/v1 # Optional: Custom base URL for OpenAI-compatible APIs

# AI Provider Configuration
# AI_PROVIDER=openai  # Options: gemini, openai
OPENAI_MODEL=gemini-2.5-pro-preview-06-05 # Default OpenAI model when using OpenAI provider
